# API Configuration
VITE_API_URL=http://localhost:3000/api

# App Configuration
VITE_APP_NAME=Gado with Helpers
VITE_APP_VERSION=1.0.0

# Environment
VITE_NODE_ENV=development

# External Services
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
VITE_GOOGLE_ANALYTICS_ID=your_google_analytics_id

# OAuth Configuration (Frontend)
VITE_GOOGLE_CLIENT_ID=your_google_client_id
VITE_APPLE_CLIENT_ID=your_apple_client_id

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_OFFLINE_MODE=true
VITE_ENABLE_PWA=true

# CDN Configuration
VITE_CDN_URL=https://your-cdn-url.com

# Sentry Configuration (if using)
VITE_SENTRY_DSN=your_sentry_dsn

# Social Media
VITE_FACEBOOK_APP_ID=your_facebook_app_id
VITE_TWITTER_API_KEY=your_twitter_api_key
