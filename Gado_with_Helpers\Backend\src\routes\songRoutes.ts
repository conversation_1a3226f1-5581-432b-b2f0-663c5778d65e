
import express, { Request, Response, NextFunction } from 'express';
import { body } from 'express-validator';
import {
  getSongs,
  getSong,
  createSong,
  incrementPlayCount,
  searchSongs,
  saveSong,
  unsaveSong,
  getSongsForOffline,
} from '../controllers/songController';
import { protect } from '../middleware/auth';
import { upload } from '../utils/fileUpload';

// Define MulterRequest interface for file uploads
interface MulterRequest extends Request {
  files: {
    coverImage?: Express.Multer.File[];
    audioFile?: Express.Multer.File[];
  };
}

const router = express.Router();

// @route   GET /api/songs
router.get('/songs', getSongs);

// @route   GET /api/songs/search
router.get('/songs/search', searchSongs);

// @route   GET /api/songs/:id
router.get('/songs/:id', getSong);

// @route   POST /api/songs
router.post(
  '/songs',
  [
    body('title').notEmpty().withMessage('Title is required'),
    body('artist').notEmpty().withMessage('Artist is required'),
    body('tribe').notEmpty().withMessage('Tribe is required'),
    body('language').notEmpty().withMessage('Language is required'),
    body('description').notEmpty().withMessage('Description is required'),
    body('duration').isNumeric().withMessage('Duration must be a number'),
    body('lyrics').optional(),
    body('tags').optional(),
  ],
  protect,
  upload.fields([
    { name: 'audioFile', maxCount: 1 },
    { name: 'coverImage', maxCount: 1 },
  ]),
  (req: Request, res: Response, next: NextFunction): Promise<Response | void> => {
    return createSong(req as MulterRequest, res, next);
  }
);

// @route   PUT /api/songs/:id/play
router.put('/songs/:id/play', incrementPlayCount);

// @route   POST /api/songs/:id/save
router.post('/songs/:id/save', protect, saveSong);

// @route   DELETE /api/songs/:id/save
router.delete('/songs/:id/save', protect, unsaveSong);

// @route   POST /api/songs/offline
router.post(
  '/offline',
  protect,
  [
    body('ids').isArray().withMessage('Song IDs must be an array'),
    body('ids.*').isMongoId().withMessage('Valid song IDs are required'),
  ],
  getSongsForOffline
);

export default router;
