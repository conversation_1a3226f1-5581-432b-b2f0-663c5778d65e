import 'dotenv/config';
import express, { Request, Response, NextFunction } from 'express';
import connectDB from './config/db';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import path from 'path';
import dotenv from 'dotenv';

// Import all routes
import authRoutes from './routes/authRoutes';
import userRoutes from './routes/userRoutes';
import videoRoutes from './routes/videoRoutes';
import songRoutes from './routes/songRoutes';
import archiveRoutes from './routes/archiveRoutes';
import messageRoutes from './routes/messageRoutes';
import commentRoutes from './routes/commentRoutes';
import tribalNameRoutes from './routes/tribalNameRoutes';
import challengeRoutes from './routes/challengeRoutes';
import adminRoutes from './routes/adminRoutes';
import dashboardRoutes from './routes/dashboardRoutes';
import analyticsRoutes from './routes/analyticsRoutes';
import searchRoutes from './routes/searchRoutes';
import recommendationRoutes from './routes/recommendationRoutes';
import notificationRoutes from './routes/notificationRoutes';
import subscriptionRoutes from './routes/subscriptionRoutes';
import premiumRoutes from './routes/premiumRoutes';
import marketPlaceRoutes from './routes/marketPlaceRoutes';
import partnershipRoutes from './routes/partnershipRoutes';
import integrationRoutes from './routes/integrationRoutes';
import culturalContextRoutes from './routes/culturalContextRoutes';
import languageRoutes from './routes/languageRoutes';
import geoDiscoveryRoutes from './routes/geoDiscoveryRoutes';
import moderationRoutes from './routes/moderationRoutes';
import contributionsRoutes from './routes/contributionsRoutes';
import syncRoutes from './routes/syncRoutes';
import downloadsRoutes from './routes/downloadsRoutes';
import exportRoutes from './routes/exportRoutes';
import userPreferenceRoutes from './routes/userPreferenceRoutes';

// Import middleware
import { errorHandler } from './middleware/errorHandler';

// Load env vars
dotenv.config();

const app = express();

// Connect to MongoDB
connectDB();

// Middleware
app.use(helmet());
app.use(
  cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:5173',
    credentials: true,
  })
);
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(morgan('dev'));

// Mount routes with /api prefix
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api', videoRoutes);
app.use('/api', songRoutes);
app.use('/api/archives', archiveRoutes);
app.use('/api/messages', messageRoutes);
app.use('/api/comments', commentRoutes);
app.use('/api/tribal-names', tribalNameRoutes);
app.use('/api/challenges', challengeRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/search', searchRoutes);
app.use('/api/recommendations', recommendationRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/premium', premiumRoutes);
app.use('/api/marketplace', marketPlaceRoutes);
app.use('/api/partnerships', partnershipRoutes);
app.use('/api/integrations', integrationRoutes);
app.use('/api/cultural-context', culturalContextRoutes);
app.use('/api/languages', languageRoutes);
app.use('/api/geo-discovery', geoDiscoveryRoutes);
app.use('/api/moderation', moderationRoutes);
app.use('/api/contributions', contributionsRoutes);
app.use('/api/sync', syncRoutes);
app.use('/api/downloads', downloadsRoutes);
app.use('/api/export', exportRoutes);
app.use('/api/user-preferences', userPreferenceRoutes);

// Health check endpoint
app.get('/api/health', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'OK',
    message: 'Server is running',
    timestamp: new Date().toISOString(),
  });
});

// 404 handler for API routes
app.use('/api/*', (req: Request, res: Response) => {
  res.status(404).json({
    error: 'API endpoint not found',
    path: req.path,
  });
});

// Global error handler
app.use(errorHandler);

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
});

export default app;
