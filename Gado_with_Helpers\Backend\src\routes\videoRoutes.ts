
import express, { Request, Response, NextFunction } from 'express';
import { body } from 'express-validator';
import {
  getVideos,
  getVideo,
  createVideo,
  incrementViewCount,
  searchVideos,
  saveVideo,
  unsaveVideo,
  getVideosForOffline,
} from '../controllers/videoController';
import { protect } from '../middleware/auth';
import { upload } from '../utils/fileUpload';

// Define MulterRequest interface for file uploads
interface MulterRequest extends Request {
  files: {
    [fieldname: string]: Express.Multer.File[];
  };
}

const router = express.Router();

// @route   GET /api/videos
router.get('/videos', getVideos);

// @route   GET /api/videos/search
router.get('/videos/search', searchVideos);

// @route   GET /api/videos/:id
router.get('/videos/:id', getVideo);

// @route   POST /api/videos
router.post(
  '/videos',
  protect,
  [
    body('title').notEmpty().withMessage('Title is required'),
    body('description').notEmpty().withMessage('Description is required'),
    body('tribe').notEmpty().withMessage('Tribe is required'),
    body('language').notEmpty().withMessage('Language is required'),
    body('category').notEmpty().withMessage('Category is required'),
    body('duration').isNumeric().withMessage('Duration must be a number'),
    body('tags').optional(),
  ],
  upload.fields([
    { name: 'videoFile', maxCount: 1 },
    { name: 'thumbnailImage', maxCount: 1 },
  ]),
  (req: Request, res: Response, next: NextFunction): Promise<void> => {
    return createVideo(req as MulterRequest, res, next);
  }
);

// @route   PUT /api/videos/:id/view
router.put('/videos/:id/view', incrementViewCount);

// @route   POST /api/videos/:id/save
router.post('/videos/:id/save', protect, saveVideo);

// @route   DELETE /api/videos/:id/save
router.delete('/videos/:id/save', protect, unsaveVideo);

// @route   POST /api/videos/offline
router.post(
  '/offline',
  protect,
  [
    body('ids').isArray().withMessage('Video IDs must be an array'),
    body('ids.*').isMongoId().withMessage('Valid video IDs are required'),
  ],
  getVideosForOffline
);

export default router;
