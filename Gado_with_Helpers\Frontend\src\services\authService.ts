import api from './api';
import { User } from '../../../shared/src/types/user';

// Get user ID from local storage or current user
export const getUserId = async (): Promise<string> => {
  try {
    const user = await getCurrentUser();
    return user?.id || getAnonymousId();
  } catch {
    console.error('Error getting user ID:', Error);
    return getAnonymousId();
  }
};

// Get or create anonymous ID
const getAnonymousId = (): string => {
  let anonymousId = localStorage.getItem('anonymousId');

  if (!anonymousId) {
    anonymousId = `anon_${generateUniqueId()}`;
    localStorage.setItem('anonymousId', anonymousId);
  }

  return anonymousId;
};

// Generate a unique ID
const generateUniqueId = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

// Get current user from local storage or API
export const getCurrentUser = async (): Promise<User | null> => {
  // Check if we have user in localStorage
  const userJson = localStorage.getItem('user');
  if (userJson) {
    try {
      return JSON.parse(userJson);
    } catch {
      console.error('Error parsing user from localStorage:', Error);
    }
  }

  // If not, try to fetch from API
  try {
    const response = await api.get('/auth/me');
    const user = response.data;

    // Save to localStorage for future use
    localStorage.setItem('user', JSON.stringify(user));

    return user;
  } catch (error) {
    console.error('Error fetching user from API:', error);
    // User is not authenticated
    return null;
  }
};

// Check if user is authenticated
export const isAuthenticated = async (): Promise<boolean> => {
  const user = await getCurrentUser();
  return !!user;
};

// Logout user
export const logout = async (): Promise<void> => {
  try {
    await api.post('/auth/logout');
  } catch {
    console.error('Error during logout');
  } finally {
    // Clear local storage regardless of API success
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');

    // Redirect to login page
    window.location.href = '/login';
  }
};

// Refresh access token
export const refreshToken = async (): Promise<string | null> => {
  try {
    const refreshToken = localStorage.getItem('refreshToken');

    if (!refreshToken) {
      return null;
    }

    const response = await api.post('/auth/refresh', { refreshToken });

    // Update tokens in localStorage
    localStorage.setItem('accessToken', response.data.accessToken);
    localStorage.setItem('refreshToken', response.data.refreshToken);

    return response.data.accessToken;
  } catch (error) {
    console.error('Error refreshing token:', error);
    return null;
  }
};
