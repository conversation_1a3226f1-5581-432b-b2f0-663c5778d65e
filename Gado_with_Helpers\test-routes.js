const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testRoutes() {
  console.log('Testing backend routes...\n');

  const routes = [
    { method: 'GET', path: '/health', description: 'Health check' },
    { method: 'GET', path: '/videos', description: 'Get videos' },
    { method: 'GET', path: '/songs', description: 'Get songs' },
    { method: 'GET', path: '/archives', description: 'Get archives' },
    { method: 'GET', path: '/challenges', description: 'Get challenges' },
  ];

  for (const route of routes) {
    try {
      const response = await axios({
        method: route.method,
        url: `${BASE_URL}${route.path}`,
        timeout: 5000,
        validateStatus: () => true // Accept any status code
      });

      console.log(`✅ ${route.method} ${route.path} - ${route.description}`);
      console.log(`   Status: ${response.status}`);
      console.log(`   Response: ${JSON.stringify(response.data).substring(0, 100)}...\n`);
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${route.method} ${route.path} - Server not running`);
        console.log('   Please start the backend server first\n');
        break;
      } else {
        console.log(`❌ ${route.method} ${route.path} - Error: ${error.message}\n`);
      }
    }
  }
}

testRoutes().catch(console.error);
